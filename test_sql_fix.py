#!/usr/bin/env python3
"""
Test script to verify the SQL binding fix for get_order_info_for_active_network
"""

import sys
import os

# Add the current directory to the path so we can import SQLiteClass
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from SQLiteClass import SQLiteClass

def test_sql_binding():
    """Test that the SQL binding issue is fixed"""
    print("Testing SQL binding fix...")
    
    db = SQLiteClass()
    
    # Test the method that was causing the binding error
    try:
        # This should not raise a binding error anymore
        result = db.get_order_info_for_active_network("123", "testpassword")
        print(f"✓ get_order_info_for_active_network executed successfully")
        print(f"  Result: {result}")
        return True
    except Exception as e:
        print(f"✗ get_order_info_for_active_network failed: {e}")
        return False

def test_select_query_variations():
    """Test different parameter passing styles to select_query"""
    print("\nTesting select_query parameter variations...")
    
    db = SQLiteClass()
    
    try:
        # Test 1: Single parameter as individual argument
        result1 = db.select_query("SELECT COUNT(*) as count FROM orders WHERE unit = ?", "123")
        print(f"✓ Single parameter (individual arg): {result1}")
        
        # Test 2: Single parameter as tuple
        result2 = db.select_query("SELECT COUNT(*) as count FROM orders WHERE unit = ?", ("123",))
        print(f"✓ Single parameter (tuple): {result2}")
        
        # Test 3: Multiple parameters as individual arguments
        result3 = db.select_query("SELECT COUNT(*) as count FROM orders WHERE unit = ? AND et_passwd = ?", "123", "testpass")
        print(f"✓ Multiple parameters (individual args): {result3}")
        
        # Test 4: Multiple parameters as tuple
        result4 = db.select_query("SELECT COUNT(*) as count FROM orders WHERE unit = ? AND et_passwd = ?", ("123", "testpass"))
        print(f"✓ Multiple parameters (tuple): {result4}")
        
        return True
    except Exception as e:
        print(f"✗ select_query variations failed: {e}")
        return False

if __name__ == "__main__":
    print("SQL Binding Fix Test")
    print("=" * 50)
    
    success1 = test_sql_binding()
    success2 = test_select_query_variations()
    
    if success1 and success2:
        print("\n✓ All tests passed! The SQL binding fix is working.")
        sys.exit(0)
    else:
        print("\n✗ Some tests failed.")
        sys.exit(1)
