import os
import sqlite3
from datetime import datetime, timedelta, date
from calendar import monthrange
from typing import overload, Tuple, Optional, Union, Dict, Any, List

from mtikClass import mtikClass


class SQLiteClass:
    def connect_sqlite(self):
        """Establish SQLite connection and debug print absolute path."""
        db_path = os.path.abspath("hotspotAPI.sqlite")
        connection = sqlite3.connect(db_path)
        connection.row_factory = sqlite3.Row  # Enables dictionary-like row access
        return connection

    def select_query(self, query, *params):
        """Executes a SELECT query and returns results as dictionaries."""
        # Fix: Unpack the tuple if it's a single-element tuple containing another tuple
        if len(params) == 1 and isinstance(params[0], tuple):
            params = params[0]

        with self.connect_sqlite() as connection:
            cursor = connection.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]

    def save_query(self, query, *params, returnNewIDInsteadOfRowCount=False):
        with self.connect_sqlite() as connection:
            print(f"query: {query}\nparams: {params}\n")
            # Fix: Unpack the tuple if it's a single-element tuple containing another tuple
            if len(params) == 1 and isinstance(params[0], tuple):
                params = params[0]

            # this is test comment for testing out remote_api_check.yml
            # Replace %s placeholders with ? for SQLite
            query = query.replace("%s", "?")
            print(f"Params after flatten: {params}")
            print("Param types:", [type(x) for x in params])
            cursor = connection.execute(query, params)
            connection.commit()
            return cursor.lastrowid if returnNewIDInsteadOfRowCount else cursor.rowcount

    def get_router(self, ipaddress):
        return self.select_query("SELECT hostname FROM router WHERE ipaddress = ?", (ipaddress,))

    def router_turn_rental_off(self, ipaddress):
        if ipaddress is None:
            return 0
        # Ensure ipaddress is a string
        ipaddress = str(ipaddress)
        return self.save_query("UPDATE router SET rental = 0 WHERE ipaddress = ?", ipaddress)

    def update_nas(self, hostname, ipaddress):
        return self.save_query("UPDATE nas SET ipaddress = ? WHERE shortname = ?", (ipaddress, hostname))

    def save_router(self, params):
        """Insert or update router information."""
        existing = self.select_query("SELECT * FROM router WHERE hostname = ?", (params['hostname'],))
        if existing:
            query = """
                UPDATE router 
                SET model = ?, firmware = ?, rental = ?, ssid_24 = ?, freq_24 = ?, 
                    ssid_5 = ?, freq_5 = ?, ssid_pwd = ?, ssid_clients = ?, queue_name = ?, ipaddress = ?
                WHERE hostname = ?
            """
            values = (
                str(params['model']),  # <-- Ensure model is a string for SQLite!
                params['firmware'],
                params['rental'],
                params['ssid_24'],
                params['freq_24'],
                params['ssid_5'],
                params['freq_5'],
                params['ssid_pwd'],
                params['ssid_clients'],
                params['queue_name'],
                params['ipaddress'],
                params['hostname']
            )
        else:
            query = """
                INSERT INTO router (hostname, model, firmware, rental, ssid_24, freq_24, ssid_5, freq_5, 
                                   ssid_pwd, ssid_clients, queue_name, ipaddress)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            values = (
                params['hostname'],
                str(params['model']),  # <-- Ensure model is a string for SQLite!
                params['firmware'],
                params['rental'],
                params['ssid_24'],
                params['freq_24'],
                params['ssid_5'],
                params['freq_5'],
                params['ssid_pwd'],
                params['ssid_clients'],
                params['queue_name'],
                params['ipaddress']
            )
            print("[DEBUG] values being sent to save_query:", values)
            print("[DEBUG] types:", [type(v) for v in values])

        return self.save_query(query, values)

    def get_routers(self):
        return self.select_query("SELECT hostname, model, firmware, rental, queue_name, ipaddress FROM router")

    def get_unit_orders(self, id):
        return self.select_query("SELECT price, transactionID, approval, created_at FROM orders WHERE unit = ?", id)

    def get_unit_router_ip(self, id):
        unit_id = 'et' + str(id)
        result = self.select_query("SELECT ipaddress FROM nas WHERE shortname = ?", unit_id)
        return result[0]['ipaddress'] if result else None

    def finalize_new_order(self, id):
        unit_id = 'et' + str(id)
        result = self.select_query("SELECT ipaddress FROM nas WHERE shortname = ?", unit_id)
        return result[0]['ipaddress'] if result else None

    def get_unit_by_ipaddress(self, ipaddress):
        result = self.select_query("SELECT shortname FROM nas WHERE ipaddress = ?", ipaddress)
        if result:
            shortname = result[0]['shortname']
            # Remove 'et' prefix if present
            if shortname.startswith('et'):
                return shortname[2:]
            return shortname
        return None

    def get_unit_id(self, id):
        unit_id = 'et' + str(id)
        result = self.select_query("SELECT id FROM nas WHERE shortname = ?", unit_id)
        return result[0]['id'] if result else None

    def new_network(self, unit, password, days, package, email, price, hard_expiration_date, isTest=0, origin=None):
        print(f"new_network: {unit} {password} {days} {package} {email}")

        unit_id = self.get_unit_id(unit)
        if unit_id is None:
            return None, "Could not find unit. Please check your spelling and try again."
        print("unit_id: ", unit_id)
        speed = 50 if package == 'streaming' else 25
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        sql_statement = "INSERT INTO orders (et_nasid, et_passwd, speed, et_days, created_at, unit, email, package, price, hard_expiration_date, isTest, origin) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        params = (unit_id, password, speed, days, now, unit, email, package, price, hard_expiration_date, isTest, origin)
        return self.save_query(sql_statement, params, returnNewIDInsteadOfRowCount=True), "Success."

    def get_active_networks(self):
        results = self.select_query(
            """
                SELECT orders.et_passwd as passwd, orders.speed as speed, orders.et_days as days, 
                                orders.created_at as created_at, nas.shortname as shortname, 
                                orders.hard_expiration_date as hard_expiration_date, isTest as isTest
                         FROM orders
                         INNER JOIN nas ON orders.et_nasid = nas.id
                group by shortname
                having max(created_at)
            """
        )

        response = []
        now = date.today()
        for result in results:
            created_at = datetime.strptime(result['created_at'], '%Y-%m-%d %H:%M:%S').date()

            # Calculate days left based on hard_expiration_date if it exists
            if result['hard_expiration_date'] and result['hard_expiration_date'] != 'None':
                hard_expiration = datetime.strptime(result['hard_expiration_date'], '%Y-%m-%d').date()
                days_left = (hard_expiration - now).days
                # If hard expiration date has passed, set days_left to 0
                if days_left <= 0:
                    days_left = 0
            else:
                # Original calculation if no hard expiration date
                target = created_at + timedelta(days=int(result['days']))
                days_left = (target - now).days

            if days_left >= 0:
                response.append({
                    'passwd': result['passwd'],
                    'speed': result['speed'],
                    'days': result['days'],
                    'daysLeft': days_left,
                    'unit': result['shortname'],
                    'shortname': f'<a href="unit/{result["shortname"][2:]}">{result["shortname"][2:]}</a>',
                    'isTest': result['isTest']
                })
        return response

    def get_order_info_for_active_network(self, unit, password):
        """
        Get order information for an active network based on unit and password.

        Args:
            unit: Unit identifier
            password: Network password

        Returns:
            List of order dictionaries or empty list if no matches
        """
        try:
            # Ensure parameters are properly formatted
            unit = str(unit).strip() if unit else ""
            password = str(password).strip() if password else ""

            if not unit or not password:
                print(f"[DEBUG] get_order_info_for_active_network: Missing parameters - unit: '{unit}', password: '{password}'")
                return []

            print(f"[DEBUG] get_order_info_for_active_network: Querying with unit='{unit}', password='{password}'")

            result = self.select_query(
                """
                    SELECT id as order_id, et_days, package, created_at, hard_expiration_date, isTest
                    FROM orders
                    WHERE unit = ? and et_passwd = ?
                    ORDER BY created_at DESC
                    LIMIT 1
                """, unit, password  # Pass as separate arguments to match select_query signature
            )

            print(f"[DEBUG] get_order_info_for_active_network: Query result: {result}")
            return result

        except Exception as e:
            print(f"[ERROR] get_order_info_for_active_network: {str(e)}")
            return []



    def get_recent_order_info(self):
        """Retrieve recent (past 2 days) general order info."""
        today = date.today()
        two_days_ago = today - timedelta(days=2)

        order_info = self.select_query(f"""
            SELECT email, price, package, unit, et_passwd, speed, et_days, isPaid, isTest as isTest
            FROM orders  
            WHERE date(created_at) BETWEEN date('{two_days_ago.strftime("%Y-%m-%d")}') 
            AND date('{today.strftime("%Y-%m-%d")}') 
            ORDER BY created_at ASC
        """)

        customer_info_list = []

        if order_info:
            for order in order_info:
                summary = (f"pwd: {order['et_passwd']} "
                           f"pkg: {order['et_days']} of {order['package']} x{order['speed']} "
                           f"for unit {order['unit']}")

                status = "Confirmed" if order['isPaid'] == 1 else "Pending"

                order_data = {
                    "email": order["email"],
                    "status": status,
                    "order_summary": summary,
                    "isTest": order["isTest"]
                }

                customer_info_list.append(order_data)

        return customer_info_list if customer_info_list else None

    def get_orders_count(self):
        result = self.select_query("SELECT COUNT(*) as count FROM orders")
        return result[0]['count'] if result else 0

    def get_orders_mtd(self):
        """Retrieve month-to-date orders."""
        today = date.today()
        return self.select_query(
            f"""
            SELECT price, unit, transactionID, approval, created_at, isTest as isTest 
            FROM orders  
            WHERE date(created_at) BETWEEN date('{today.replace(day=1).strftime("%Y-%m-%d")}') 
            AND date('{today.replace(day=monthrange(today.year, today.month)[1]).strftime("%Y-%m-%d")}') 
            ORDER BY created_at ASC
            """
        )

    def update_order(self, order_id, transaction_id, receipt_url, connect_net):
        """Finalize order with transactional information and receipt url."""
        sql_statement = """
                UPDATE orders
            SET transactionID = ?,
                receipt_url = ?,
                       isPaid = 1
            WHERE id = ?
                       """
        row_count = self.save_query(sql_statement, (transaction_id, receipt_url, order_id))

        order_info_for_update = self.select_query("SELECT unit, et_passwd, package FROM orders WHERE id = ?", order_id)

        if order_info_for_update:
            unit = order_info_for_update[0]['unit']
            password = order_info_for_update[0]['et_passwd']
            package = order_info_for_update[0]['package']
        else:
            return None, f"No Order Info associated with order_id {order_id}"

        ipaddress = self.get_unit_router_ip(unit)

        if row_count < 1:
            return None
        if  bool(connect_net):
            mstik = mtikClass()
            if mstik is None:
                return 0
            try:
                success, message = mstik.new_rental(ipaddress, unit, password, package)
                # this could be delayed but since we are going to expire payment links after 24 hours,  a whole new transaction is required
                if not success:
                    return 0, "Could Not Create Network"
            except Exception as e:
                print(f"Error in new_rental: {e}")
                return 0,

        email = None
        select_statement = "SELECT email, et_passwd FROM orders WHERE id = ?"
        result = self.select_query(select_statement, order_id)

        if result:
            email = result[0].get('email', None)
            et_passwd = result[0].get('et_passwd', None)
            return email, et_passwd

        return None,"Email and Password Not Found"

    def expire_active_network(self, ipaddress):
        """
        Force immediate expiration of an active network by setting hard_expiration_date to 5 minutes ago.

        Args:
            ipaddress: Router IP address

        Returns:
            Tuple: (success: bool, message: str, affected_rows: int)
        """
        EXPIRATION_OFFSET_MINUTES = 5

        try:
            # Find the unit for this IP address
            unit = self.get_unit_by_ipaddress(ipaddress)
            if not unit:
                return False, f"No unit found for IP address {ipaddress}", 0

            # Get the most recent active order for this unit
            order_result = self.select_query(
                """
          SELECT orders.id as order_id, orders.hard_expiration_date as hard_expiration_date,
                       orders.created_at as created_at, orders.et_days as et_days,
                       nas.shortname as shortname
                FROM orders
                INNER JOIN nas ON orders.et_nasid = nas.id
                WHERE unit = ?
                GROUP BY shortname
                HAVING max(created_at)
                """, unit
            )

            if not order_result:
                return False, f"No orders found for unit {unit}", 0

            order = order_result[0]
            order_id = order['order_id']
            shortname = order['shortname']
            current_hard_expiration = order['hard_expiration_date']
            created_at = datetime.strptime(order['created_at'], "%Y-%m-%d %H:%M:%S")
            et_days = order['et_days']

            # Check if network is already expired
            now = datetime.now()
            if self._is_network_expired(current_hard_expiration, created_at, et_days, now):
                return False, f"Network for unit {unit} is already expired", 0

            # Set hard_expiration_date to force immediate expiration
            expiration_time = now - timedelta(minutes=EXPIRATION_OFFSET_MINUTES)
            expiration_date_str = expiration_time.strftime('%Y-%m-%d')

            # Update the order with the new hard expiration date
            affected_rows = self.save_query(
                "UPDATE orders SET hard_expiration_date = ? WHERE id = ?",
                (expiration_date_str, order_id)
            )

            if affected_rows > 0:
                return True, f"Successfully expired network for unit {unit} (IP: {ipaddress})", affected_rows
            else:
                return False, f"Failed to update expiration for unit {unit}", 0

        except Exception as e:
            return False, f"Error expiring network: {str(e)}", 0

    def _is_network_expired(self, hard_expiration_date, created_at, et_days, current_time):
        """
        Helper method to check if a network is already expired.

        Args:
            hard_expiration_date: Hard expiration date string or None
            created_at: Order creation datetime
            et_days: Number of days for the order
            current_time: Current datetime

        Returns:
            bool: True if network is expired, False otherwise
        """
        if hard_expiration_date and hard_expiration_date != 'None':
            # Check hard expiration date
            hard_expiration = datetime.strptime(hard_expiration_date, '%Y-%m-%d')
            return hard_expiration.date() <= current_time.date()
        else:
            # Check natural expiration based on created_at + et_days
            natural_expiration = created_at + timedelta(days=int(et_days))
            return natural_expiration <= current_time

    def does_rental_exist(self, unit):
        """
        Check if a rental exists for the given unit.

        Args:
            unit: Unit identifier

        Returns:
            Tuple: (profile_exists: bool, rental_order_info: list)
        """
        try:
            # Input validation
            if not unit:
                print(f"[ERROR] does_rental_exist: Unit parameter is empty")
                return False, []

            unit = str(unit).strip()
            print(f"[DEBUG] does_rental_exist: Checking unit '{unit}'")

            # Get router IP for the unit
            ipaddress = self.get_unit_router_ip(unit)
            if not ipaddress:
                print(f"[ERROR] does_rental_exist: No IP address found for unit '{unit}'")
                return False, []

            print(f"[DEBUG] does_rental_exist: Found IP address '{ipaddress}' for unit '{unit}'")

            # Initialize mtik connection
            mstik = mtikClass()
            if mstik is None:
                print(f"[ERROR] does_rental_exist: Failed to initialize mtikClass")
                return False, []

            # Check if security profile exists on router
            rental_order_info = []
            try:
                mstik_exists = mstik.security_profile_exists(ipaddress)
                if not isinstance(mstik_exists, (list, tuple)) or len(mstik_exists) < 2:
                    print(f"[ERROR] does_rental_exist: Invalid response from security_profile_exists: {mstik_exists}")
                    return False, []

                profile_exists = mstik_exists[0]
                password = mstik_exists[1]

                print(f"[DEBUG] does_rental_exist: Profile exists: {profile_exists}, Password: '{password}'")

                if profile_exists and password:
                    rental_order_info = self.get_order_info_for_active_network(unit, password)
                    print(f"[DEBUG] does_rental_exist: Retrieved rental order info: {rental_order_info}")

                return profile_exists, rental_order_info

            except Exception as mtik_error:
                print(f"[ERROR] does_rental_exist: Error checking router security profile: {str(mtik_error)}")
                return False, []

        except Exception as e:
            print(f"[ERROR] does_rental_exist: Unexpected error for unit '{unit}': {str(e)}")
            return False, []

    def get_router_profile_information  (self, unit):
        ipaddress = self.get_unit_router_ip(unit)
        mstik = mtikClass()
        if mstik is None:
            return 0
        return mstik.get_router_profile_information(ipaddress)